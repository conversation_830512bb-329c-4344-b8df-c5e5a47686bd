<template>
  <div>
    <div class="back-icon" v-if="(enter && levelHistory.length !== 0) || (enter && !isPreview())" @click="backLevel">
      <n-icon :color="backColor" :size="backSize * 1.1">
        <ArrowBackIcon />
      </n-icon>
      <span
        :style="{
          'font-weight': 200,
          color: backColor,
          'font-size': `${backSize}px`
        }"
      >
        返回上级
      </span>
    </div>
    <v-chart
      ref="vChartRef"
      :init-options="initOptions"
      :theme="themeColor"
      :option="option.value"
      :manual-update="isPreview()"
      autoresize
      @click="chartPEvents"
    >
    </v-chart>
  </div>
</template>

<script setup lang="ts">
import { PropType, reactive, watch, ref, nextTick, toRefs, onBeforeUnmount } from 'vue'
import config, { includes } from './config'
import VChart from 'vue-echarts'
import { icon } from '@/plugins'
import { useCanvasInitOptions } from '@/hooks/useCanvasInitOptions.hook'
import { use, registerMap } from 'echarts/core'
import { EffectScatterChart, MapChart } from 'echarts/charts'
import { CanvasRenderer } from 'echarts/renderers'
import { useChartDataFetch } from '@/hooks'
import { mergeTheme, setOption } from '@/packages/public/chart'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import { isPreview } from '@/utils'
import mapJsonWithoutHainanIsLands from '../MapBase/mapWithoutHainanIsLands.json'
import mapChinaJson from '../MapBase/mapGeojson/china.json'
import { DatasetComponent, GridComponent, TooltipComponent, GeoComponent, VisualMapComponent } from 'echarts/components'

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})

const { ArrowBackIcon } = icon.ionicons5
let levelHistory: any = ref([])

const { backColor, backSize, enter } = toRefs(props.chartConfig.option.mapRegion)
const initOptions = useCanvasInitOptions(props.chartConfig.option, props.themeSetting)

use([
  MapChart,
  DatasetComponent,
  CanvasRenderer,
  GridComponent,
  TooltipComponent,
  GeoComponent,
  EffectScatterChart,
  VisualMapComponent
])

const option = reactive({
  value: mergeTheme(props.chartConfig.option, props.themeSetting, includes)
})
const vChartRef = ref<typeof VChart>()

//动态获取json注册地图
const getGeojson = (regionId: string) => {
  return new Promise<boolean>(resolve => {
    import(`../MapBase/mapGeojson/${regionId}.json`).then(data => {
      registerMap(regionId, { geoJSON: data.default as any, specialAreas: {} })
      resolve(true)
    })
  })
}

//异步时先注册空的 保证初始化不报错
registerMap(`${props.chartConfig.option.mapRegion.adcode}`, { geoJSON: {} as any, specialAreas: {} })

// 进行更换初始化地图 如果为china 单独处理
const registerMapInitAsync = async () => {
  try {
    await nextTick()
    const adCode = `${props.chartConfig.option.mapRegion.adcode}`
    if (adCode !== 'china') {
      await getGeojson(adCode)
    } else {
      await hainanLandsHandle(props.chartConfig.option.mapRegion.showHainanIsLands)
    }

    // 处理初始数据和配置
    const initialData = props.chartConfig.option.dataset
    await dataSetHandle(initialData)
  } catch (error) {
    console.warn('MapBubble初始化错误:', error)
    // 即使出错也要尝试渲染基本配置
    vEchartsSetOption()
  }
}
registerMapInitAsync()

// 手动触发渲染
const vEchartsSetOption = () => {
  try {
    if (!props.chartConfig?.option) {
      console.warn('MapBubble: chartConfig.option不存在')
      return
    }
    option.value = props.chartConfig.option
    setOption(vChartRef.value, props.chartConfig.option)
  } catch (error) {
    console.error('MapBubble vEchartsSetOption error:', error)
  }
}

// 更新数据处理
const dataSetHandle = async (dataset: any) => {
  try {
    if (!props.chartConfig?.option?.series) {
      console.warn('MapBubble: series配置不存在')
      return
    }

    props.chartConfig.option.series.forEach((item: any) => {
      if (item.type === 'effectScatter') {
        // 设置数据
        if (dataset && Array.isArray(dataset) && dataset.length > 0) {
          item.data = dataset.map((dataItem: any) => ({
            name: dataItem.name || '',
            value: dataItem.value || [0, 0, 0],
            label: dataItem.label || dataItem.name || ''
          }))
        }

        // 应用气泡配置（无论是否有数据都要应用）
        const bubbleConfig = props.chartConfig.option.bubbleConfig
        console.log('🔍 [DEBUG] 气泡配置:', bubbleConfig)
        if (bubbleConfig) {
          item.symbolSize = function(val: any) {
            if (!val || !Array.isArray(val) || val.length < 3) return bubbleConfig.symbolSize[0]
            const minSize = bubbleConfig.symbolSize[0] || 20
            const maxSize = bubbleConfig.symbolSize[1] || 40
            return Math.max(minSize, Math.min(maxSize, val[2] / 50))
          }

          // 确保itemStyle对象存在
          if (!item.itemStyle) {
            item.itemStyle = {}
          }

          // 应用气泡颜色配置
          item.itemStyle.color = bubbleConfig.bubbleColor || '#DE3D3DFF'
          item.itemStyle.borderColor = bubbleConfig.borderColor || '#FFFFFF'
          item.itemStyle.borderWidth = bubbleConfig.borderWidth || 2
          item.itemStyle.shadowColor = bubbleConfig.shadowColor || bubbleConfig.bubbleColor || '#DE3D3DFF'
          item.itemStyle.shadowBlur = bubbleConfig.shadowBlur || 10

          console.log('🔍 [DEBUG] 应用的气泡颜色:', item.itemStyle.color)

          // 应用动画效果配置
          if (bubbleConfig.showEffect) {
            if (!item.rippleEffect) {
              item.rippleEffect = {}
            }
            item.rippleEffect.scale = bubbleConfig.effectScale || 6
            item.rippleEffect.color = bubbleConfig.effectColor || '#81d4fa'
            item.rippleEffect.brushType = 'fill'
          } else {
            // 如果关闭动画效果，移除rippleEffect
            delete item.rippleEffect
          }
        }

        // 应用标签配置
        const labelConfig = props.chartConfig.option.labelConfig
        if (labelConfig) {
          // 确保label对象存在
          if (!item.label) {
            item.label = {}
          }

          // 应用标签配置
          item.label.show = labelConfig.show !== false
          item.label.formatter = function(params: any) {
            return params?.data?.label || params?.data?.name || ''
          }
          item.label.fontSize = labelConfig.fontSize || 12
          item.label.color = labelConfig.color || '#FFFFFF'
          item.label.fontWeight = labelConfig.fontWeight || 'bold'
          item.label.position = labelConfig.position || 'top'
          item.label.offset = labelConfig.offset || [0, -10]
          item.label.textBorderColor = labelConfig.textBorderColor || '#000000'
          item.label.textBorderWidth = labelConfig.textBorderWidth || 1
          item.label.textShadowColor = labelConfig.textShadowColor || '#000000'
          item.label.textShadowBlur = labelConfig.textShadowBlur || 3
        }
      }
    })
    // 移除 isPreview() 条件，让数据更新后总是重新渲染
    vEchartsSetOption()
  } catch (error) {
    console.error('MapBubble dataSetHandle error:', error)
    // 即使出错也要尝试渲染
    vEchartsSetOption()
  }
}

// 处理海南群岛
const hainanLandsHandle = async (newData: boolean) => {
  if (newData) {
    await getGeojson('china')
  } else {
    registerMap('china', { geoJSON: mapJsonWithoutHainanIsLands as any, specialAreas: {} })
  }
}

// 点击区域
const chartPEvents = (e: any) => {
  if (e.seriesType !== 'map') return
  if (!props.chartConfig.option.mapRegion.enter) {
    return
  }
  mapChinaJson.features.forEach(item => {
    var pattern = new RegExp(e.name)
    if (pattern.test(item.properties.name)) {
      let code = String(item.properties.adcode)
      levelHistory.value.push(code)
      checkOrMap(code)
    }
  })
}

// 返回上一级
const backLevel = () => {
  levelHistory.value = []
  if (levelHistory.value.length > 1) {
    levelHistory.value.pop()
    const code = levelHistory[levelHistory.value.length - 1]
    checkOrMap(code)
  } else {
    checkOrMap('china')
  }
}

// 切换地图
const checkOrMap = async (newData: string) => {
  if (newData === 'china') {
    if (props.chartConfig.option.mapRegion.showHainanIsLands) {
      hainanLandsHandle(true)
      vEchartsSetOption()
    } else {
      hainanLandsHandle(false)
      vEchartsSetOption()
    }
  } else {
    await getGeojson(newData)
  }
  props.chartConfig.option.geo.map = newData
  props.chartConfig.option.series.forEach((item: any) => {
    if (item.type === 'effectScatter') item.coordinateSystem = 'geo'
  })
  vEchartsSetOption()
}

watch(
  () => props.chartConfig.option.mapRegion.adcode,
  (newData: string) => {
    checkOrMap(newData)
  }
)

watch(
  () => props.chartConfig.option.mapRegion.showHainanIsLands,
  (newData: boolean) => {
    if (props.chartConfig.option.mapRegion.adcode === 'china') {
      hainanLandsHandle(newData)
      vEchartsSetOption()
    }
  }
)

// 预览
useChartDataFetch(props.chartConfig, useChartEditStore, (newData: any) => {
  dataSetHandle(newData)
})

// 监听数据变化
watch(
  () => props.chartConfig?.option?.dataset,
  (newData: any) => {
    try {
      dataSetHandle(newData)
    } catch (error) {
      console.error('MapBubble dataset watch error:', error)
    }
  },
  { deep: true, immediate: true }
)

// 监听气泡配置变化
watch(
  () => props.chartConfig?.option?.bubbleConfig,
  () => {
    try {
      const currentData = props.chartConfig?.option?.dataset
      dataSetHandle(currentData)
    } catch (error) {
      console.error('MapBubble bubbleConfig watch error:', error)
    }
  },
  { deep: true }
)

// 监听标签配置变化
watch(
  () => props.chartConfig?.option?.labelConfig,
  () => {
    try {
      const currentData = props.chartConfig?.option?.dataset
      dataSetHandle(currentData)
    } catch (error) {
      console.error('MapBubble labelConfig watch error:', error)
    }
  },
  { deep: true }
)

// 监听整个option配置变化，确保所有配置都能生效
watch(
  () => props.chartConfig?.option,
  () => {
    try {
      // 延迟一点执行，确保配置已经更新
      nextTick(() => {
        const currentData = props.chartConfig?.option?.dataset
        dataSetHandle(currentData)
      })
    } catch (error) {
      console.error('MapBubble option watch error:', error)
    }
  },
  { deep: true }
)

// 组件销毁时清理
onBeforeUnmount(() => {
  try {
    if (vChartRef.value) {
      // 清理ECharts实例
      const chartInstance = vChartRef.value
      if (chartInstance && typeof chartInstance.dispose === 'function') {
        chartInstance.dispose()
      }
    }
  } catch (error) {
    console.warn('MapBubble cleanup error:', error)
  }
})
</script>

<style lang="scss" scoped>
.back-icon {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 999;
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  
  span {
    margin-left: 5px;
  }
}
</style>

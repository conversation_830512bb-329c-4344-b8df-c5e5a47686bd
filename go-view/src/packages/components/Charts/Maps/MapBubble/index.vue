<template>
  <div>
    <div class="back-icon" v-if="(enter && levelHistory.length !== 0) || (enter && !isPreview())" @click="backLevel">
      <n-icon :color="backColor" :size="backSize * 1.1">
        <ArrowBackIcon />
      </n-icon>
      <span
        :style="{
          'font-weight': 200,
          color: backColor,
          'font-size': `${backSize}px`
        }"
      >
        返回上级
      </span>
    </div>
    <v-chart
      ref="vChartRef"
      :init-options="initOptions"
      :theme="themeColor"
      :option="option.value"
      :manual-update="isPreview()"
      autoresize
      @click="chartPEvents"
    >
    </v-chart>
  </div>
</template>

<script setup lang="ts">
import { PropType, reactive, watch, ref, nextTick, toRefs, onBeforeUnmount } from 'vue'
import config, { includes } from './config'
import VChart from 'vue-echarts'
import { icon } from '@/plugins'
import { useCanvasInitOptions } from '@/hooks/useCanvasInitOptions.hook'
import { use, registerMap } from 'echarts/core'
import { EffectScatterChart, MapChart } from 'echarts/charts'
import { CanvasRenderer } from 'echarts/renderers'
import { useChartDataFetch } from '@/hooks'
import { mergeTheme, setOption } from '@/packages/public/chart'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import { isPreview } from '@/utils'
import mapJsonWithoutHainanIsLands from '../MapBase/mapWithoutHainanIsLands.json'
import mapChinaJson from '../MapBase/mapGeojson/china.json'
import { DatasetComponent, GridComponent, TooltipComponent, GeoComponent, VisualMapComponent } from 'echarts/components'

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})

const { ArrowBackIcon } = icon.ionicons5
let levelHistory: any = ref([])

const { backColor, backSize, enter } = toRefs(props.chartConfig.option.mapRegion)
const initOptions = useCanvasInitOptions(props.chartConfig.option, props.themeSetting)

use([
  MapChart,
  DatasetComponent,
  CanvasRenderer,
  GridComponent,
  TooltipComponent,
  GeoComponent,
  EffectScatterChart,
  VisualMapComponent
])

const option = reactive({
  value: mergeTheme(props.chartConfig.option, props.themeSetting, includes)
})
const vChartRef = ref<typeof VChart>()

//动态获取json注册地图
const getGeojson = (regionId: string) => {
  return new Promise<boolean>(resolve => {
    import(`../MapBase/mapGeojson/${regionId}.json`).then(data => {
      registerMap(regionId, { geoJSON: data.default as any, specialAreas: {} })
      resolve(true)
    })
  })
}

//异步时先注册空的 保证初始化不报错
registerMap(`${props.chartConfig.option.mapRegion.adcode}`, { geoJSON: {} as any, specialAreas: {} })

// 进行更换初始化地图 如果为china 单独处理
const registerMapInitAsync = async () => {
  try {
    console.log('🔍 [MapBubble] 开始初始化地图')
    console.log('🔍 [MapBubble] 初始配置:', props.chartConfig.option)

    await nextTick()
    const adCode = `${props.chartConfig.option.mapRegion.adcode}`
    console.log('🔍 [MapBubble] 地图区域代码:', adCode)

    if (adCode !== 'china') {
      await getGeojson(adCode)
    } else {
      await hainanLandsHandle(props.chartConfig.option.mapRegion.showHainanIsLands)
    }
    console.log('🔍 [MapBubble] 地图数据加载完成')

    // 处理初始数据和配置
    const initialData = props.chartConfig.option.dataset
    console.log('🔍 [MapBubble] 初始数据:', initialData)
    await dataSetHandle(initialData)
    console.log('🔍 [MapBubble] 地图初始化完成')
  } catch (error) {
    console.warn('🔍 [MapBubble] 初始化错误:', error)
    // 即使出错也要尝试渲染基本配置
    vEchartsSetOption()
  }
}
registerMapInitAsync()

// 手动触发渲染
const vEchartsSetOption = () => {
  try {
    console.log('🔍 [MapBubble] vEchartsSetOption 开始执行')

    if (!props.chartConfig?.option) {
      console.warn('🔍 [MapBubble] chartConfig.option不存在')
      return
    }

    // 检查series中effectScatter的配置
    const effectScatterSeries = props.chartConfig.option.series.find((s: any) => s.type === 'effectScatter')
    if (effectScatterSeries) {
      console.log('🔍 [MapBubble] 渲染前effectScatter的itemStyle:', JSON.stringify(effectScatterSeries.itemStyle))
      console.log('🔍 [MapBubble] 渲染前effectScatter的data长度:', effectScatterSeries.data?.length || 0)
    }

    console.log('🔍 [MapBubble] 完整的option配置:', JSON.stringify(props.chartConfig.option, null, 2))

    option.value = props.chartConfig.option
    setOption(vChartRef.value, props.chartConfig.option)

    console.log('🔍 [MapBubble] setOption调用完成')
  } catch (error) {
    console.error('🔍 [MapBubble] vEchartsSetOption error:', error)
  }
}

// 更新数据处理
const dataSetHandle = async (dataset: any) => {
  try {
    console.log('🔍 [MapBubble] dataSetHandle 开始执行')
    console.log('🔍 [MapBubble] dataset:', dataset)
    console.log('🔍 [MapBubble] chartConfig.option:', props.chartConfig?.option)

    if (!props.chartConfig?.option?.series) {
      console.warn('🔍 [MapBubble] series配置不存在')
      return
    }

    console.log('🔍 [MapBubble] series数量:', props.chartConfig.option.series.length)

    props.chartConfig.option.series.forEach((item: any, index: number) => {
      console.log(`🔍 [MapBubble] 处理series[${index}]:`, item.type, item.name)

      if (item.type === 'effectScatter') {
        console.log('🔍 [MapBubble] 找到effectScatter类型的series')

        // 设置数据 - 为每个数据项设置itemStyle确保颜色生效
        if (dataset && Array.isArray(dataset) && dataset.length > 0) {
          const mainColor = bubbleConfig.bubbleColor || '#DE3D3DFF'
          item.data = dataset.map((dataItem: any) => ({
            name: dataItem.name || '',
            value: dataItem.value || [0, 0, 0],
            label: dataItem.label || dataItem.name || '',
            itemStyle: {
              color: mainColor,  // 在数据项级别设置颜色
              borderColor: bubbleConfig.borderColor || '#FFFFFF',
              borderWidth: bubbleConfig.borderWidth || 2,
              shadowColor: mainColor,
              shadowBlur: bubbleConfig.shadowBlur || 10
            }
          }))
          console.log('🔍 [MapBubble] 设置数据完成，数据量:', item.data.length)
          console.log('🔍 [MapBubble] 数据项颜色:', mainColor)
        }

        // 应用气泡配置
        const bubbleConfig = props.chartConfig.option.bubbleConfig
        console.log('🔍 [MapBubble] bubbleConfig:', bubbleConfig)

        if (bubbleConfig) {
          console.log('🔍 [MapBubble] 应用气泡配置...')

          item.symbolSize = function(val: any) {
            if (!val || !Array.isArray(val) || val.length < 3) return bubbleConfig.symbolSize[0] || 20
            const minSize = bubbleConfig.symbolSize[0] || 20
            const maxSize = bubbleConfig.symbolSize[1] || 40
            return Math.max(minSize, Math.min(maxSize, val[2] / 50))
          }

          // 记录原始itemStyle
          console.log('🔍 [MapBubble] 原始itemStyle:', JSON.stringify(item.itemStyle))

          // 应用样式配置 - 确保所有颜色都使用主颜色
          const mainColor = bubbleConfig.bubbleColor || '#DE3D3DFF'
          console.log('🔍 [MapBubble] 主颜色:', mainColor)

          const newItemStyle = {
            color: mainColor,
            borderColor: bubbleConfig.borderColor || '#FFFFFF',
            borderWidth: bubbleConfig.borderWidth || 2,
            shadowColor: mainColor, // 强制使用主颜色作为阴影
            shadowBlur: bubbleConfig.shadowBlur || 10
          }

          console.log('🔍 [MapBubble] 新的itemStyle:', JSON.stringify(newItemStyle))
          item.itemStyle = newItemStyle
          console.log('🔍 [MapBubble] 设置后的itemStyle:', JSON.stringify(item.itemStyle))

          // 强制设置颜色属性，确保生效
          item.color = mainColor

          // 尝试多种颜色设置方式确保生效
          if (!item.emphasis) item.emphasis = {}
          if (!item.emphasis.itemStyle) item.emphasis.itemStyle = {}
          item.emphasis.itemStyle.color = mainColor

          console.log('🔍 [MapBubble] 强制设置series.color:', mainColor)
          console.log('🔍 [MapBubble] 强制设置emphasis.itemStyle.color:', mainColor)

          // 应用动画效果 - 动画颜色也使用主颜色
          if (bubbleConfig.showEffect) {
            item.rippleEffect = {
              scale: bubbleConfig.effectScale || 6,
              color: mainColor, // 强制使用主颜色作为动画效果颜色
              brushType: 'fill'
            }
            console.log('🔍 [MapBubble] 设置动画效果:', JSON.stringify(item.rippleEffect))
          }
        } else {
          console.warn('🔍 [MapBubble] bubbleConfig不存在，使用默认配置')
        }

        // 应用标签配置
        const labelConfig = props.chartConfig.option.labelConfig
        console.log('🔍 [MapBubble] labelConfig:', labelConfig)

        if (labelConfig) {
          item.label = {
            show: labelConfig.show !== false,
            formatter: function(params: any) {
              return params?.data?.label || params?.data?.name || ''
            },
            fontSize: labelConfig.fontSize || 12,
            color: labelConfig.color || '#FFFFFF',
            fontWeight: labelConfig.fontWeight || 'bold',
            position: labelConfig.position || 'top',
            offset: labelConfig.offset || [0, -10],
            textBorderColor: labelConfig.textBorderColor || '#000000',
            textBorderWidth: labelConfig.textBorderWidth || 1,
            textShadowColor: labelConfig.textShadowColor || '#000000',
            textShadowBlur: labelConfig.textShadowBlur || 3
          }
          console.log('🔍 [MapBubble] 设置标签配置完成')
        }

        console.log('🔍 [MapBubble] 最终的series配置:', JSON.stringify(item, null, 2))
      }
    })

    console.log('🔍 [MapBubble] 准备调用vEchartsSetOption')
    // 重新渲染
    vEchartsSetOption()
    console.log('🔍 [MapBubble] vEchartsSetOption调用完成')
  } catch (error) {
    console.error('🔍 [MapBubble] dataSetHandle error:', error)
  }
}

// 处理海南群岛
const hainanLandsHandle = async (newData: boolean) => {
  if (newData) {
    await getGeojson('china')
  } else {
    registerMap('china', { geoJSON: mapJsonWithoutHainanIsLands as any, specialAreas: {} })
  }
}

// 点击区域
const chartPEvents = (e: any) => {
  if (e.seriesType !== 'map') return
  if (!props.chartConfig.option.mapRegion.enter) {
    return
  }
  mapChinaJson.features.forEach(item => {
    var pattern = new RegExp(e.name)
    if (pattern.test(item.properties.name)) {
      let code = String(item.properties.adcode)
      levelHistory.value.push(code)
      checkOrMap(code)
    }
  })
}

// 返回上一级
const backLevel = () => {
  levelHistory.value = []
  if (levelHistory.value.length > 1) {
    levelHistory.value.pop()
    const code = levelHistory[levelHistory.value.length - 1]
    checkOrMap(code)
  } else {
    checkOrMap('china')
  }
}

// 切换地图
const checkOrMap = async (newData: string) => {
  if (newData === 'china') {
    if (props.chartConfig.option.mapRegion.showHainanIsLands) {
      hainanLandsHandle(true)
      vEchartsSetOption()
    } else {
      hainanLandsHandle(false)
      vEchartsSetOption()
    }
  } else {
    await getGeojson(newData)
  }
  props.chartConfig.option.geo.map = newData
  props.chartConfig.option.series.forEach((item: any) => {
    if (item.type === 'effectScatter') item.coordinateSystem = 'geo'
  })
  vEchartsSetOption()
}

watch(
  () => props.chartConfig.option.mapRegion.adcode,
  (newData: string) => {
    checkOrMap(newData)
  }
)

watch(
  () => props.chartConfig.option.mapRegion.showHainanIsLands,
  (newData: boolean) => {
    if (props.chartConfig.option.mapRegion.adcode === 'china') {
      hainanLandsHandle(newData)
      vEchartsSetOption()
    }
  }
)

// 预览
useChartDataFetch(props.chartConfig, useChartEditStore, (newData: any) => {
  dataSetHandle(newData)
})

// 监听数据变化
watch(
  () => props.chartConfig?.option?.dataset,
  (newData: any) => {
    try {
      dataSetHandle(newData)
    } catch (error) {
      console.error('MapBubble dataset watch error:', error)
    }
  },
  { deep: true, immediate: true }
)

// 监听气泡配置变化
watch(
  () => props.chartConfig?.option?.bubbleConfig,
  (newConfig, oldConfig) => {
    try {
      console.log('🔍 [MapBubble] bubbleConfig变化')
      console.log('🔍 [MapBubble] 旧配置:', oldConfig)
      console.log('🔍 [MapBubble] 新配置:', newConfig)
      const currentData = props.chartConfig?.option?.dataset
      dataSetHandle(currentData)
    } catch (error) {
      console.error('🔍 [MapBubble] bubbleConfig watch error:', error)
    }
  },
  { deep: true }
)

// 监听标签配置变化
watch(
  () => props.chartConfig?.option?.labelConfig,
  () => {
    try {
      const currentData = props.chartConfig?.option?.dataset
      dataSetHandle(currentData)
    } catch (error) {
      console.error('MapBubble labelConfig watch error:', error)
    }
  },
  { deep: true }
)

// 监听整个option配置变化，但排除dataset避免无限循环
watch(
  () => ({
    bubbleConfig: props.chartConfig?.option?.bubbleConfig,
    labelConfig: props.chartConfig?.option?.labelConfig,
    mapRegion: props.chartConfig?.option?.mapRegion
  }),
  () => {
    try {
      // 延迟一点执行，确保配置已经更新
      nextTick(() => {
        const currentData = props.chartConfig?.option?.dataset
        dataSetHandle(currentData)
      })
    } catch (error) {
      console.error('MapBubble config watch error:', error)
    }
  },
  { deep: true }
)

// 组件销毁时清理
onBeforeUnmount(() => {
  try {
    if (vChartRef.value) {
      // 清理ECharts实例
      const chartInstance = vChartRef.value
      if (chartInstance && typeof chartInstance.dispose === 'function') {
        chartInstance.dispose()
      }
    }
  } catch (error) {
    console.warn('MapBubble cleanup error:', error)
  }
})
</script>

<style lang="scss" scoped>
.back-icon {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 999;
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  
  span {
    margin-left: 5px;
  }
}
</style>
